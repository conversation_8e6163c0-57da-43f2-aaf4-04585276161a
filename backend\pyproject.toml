[project]
name = "agent"
version = "0.0.1"
description = "Backend for the LangGraph agent"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
]
readme = "README.md"
license = { text = "MIT" }
requires-python = ">=3.11,<4.0"
dependencies = [
    "langgraph>=0.2.6",
    "langchain>=0.3.19",
    "langchain-google-genai",
    "langchain-openai",
    "python-dotenv>=1.0.1",
    "langgraph-sdk>=0.1.57",
    "langgraph-cli",
    "langgraph-api",
    "fastapi",
    "google-genai",
    "openai>=1.0.0",
]


[project.optional-dependencies]
dev = ["mypy>=1.11.1", "ruff>=0.6.1"]

[build-system]
requires = ["setuptools>=73.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.ruff]
lint.select = [
    "E",    # pycodestyle
    "F",    # pyflakes
    "I",    # isort
    "D",    # pydocstyle
    "D401", # First line should be in imperative mood
    "T201",
    "UP",
]
lint.ignore = [
    "UP006",
    "UP007",
    # We actually do want to import from typing_extensions
    "UP035",
    # Relax the convention by _not_ requiring documentation for every function parameter.
    "D417",
    "E501",
]
[tool.ruff.lint.per-file-ignores]
"tests/*" = ["D", "UP"]
[tool.ruff.lint.pydocstyle]
convention = "google"

[dependency-groups]
dev = [
    "langgraph-cli[inmem]>=0.1.71",
    "pytest>=8.3.5",
]
