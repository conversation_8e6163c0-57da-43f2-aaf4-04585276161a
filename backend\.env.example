# AI Assistant Environment Configuration
# Copy this file to .env and fill in your actual API keys

# =============================================================================
# API Keys (Required)
# =============================================================================

# Google Gemini API Key
# Get your free API key from: https://aistudio.google.com/app/apikey
# Free tier includes: 15 requests per minute, 1 million tokens per minute
GEMINI_API_KEY=your_gemini_api_key_here

# DeepSeek API Key  
# Get your API key from: https://platform.deepseek.com/api_keys
# Note: DeepSeek is a paid service with competitive pricing
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# DeepSeek Base URL (usually no need to change)
DEEPSEEK_BASE_URL=https://api.deepseek.com

# =============================================================================
# Service Configuration (Optional)
# =============================================================================

# Service Ports (change if you have conflicts)
SIMPLE_CHAT_PORT=3000
AI_SEARCH_PORT=2024
ALTERNATIVE_AI_SEARCH_PORT=2025

# =============================================================================
# Model Configuration (Optional)
# =============================================================================

# Default temperature for AI responses (0.0 to 1.0)
# Lower values = more focused, Higher values = more creative
DEFAULT_TEMPERATURE=0.7

# Maximum tokens for AI responses
DEFAULT_MAX_TOKENS=2048

# =============================================================================
# Development Configuration (Optional)
# =============================================================================

# Set to true for verbose logging
DEBUG=false

# Environment (development, production)
ENVIRONMENT=development

# =============================================================================
# Notes
# =============================================================================

# 1. At minimum, you need either GEMINI_API_KEY or DEEPSEEK_API_KEY
# 2. For best experience, configure both providers
# 3. Gemini has a generous free tier, DeepSeek is paid but cost-effective
# 4. The system will automatically detect available providers
# 5. Never commit your actual .env file to version control
