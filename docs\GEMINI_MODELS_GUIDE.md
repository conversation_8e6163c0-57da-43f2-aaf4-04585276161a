# Gemini Models Complete Guide / Gemini 模型完整指南

## 📋 目录 / Table of Contents

1. [模型概览 / Models Overview](#模型概览--models-overview)
2. [免费模型 / Free Models](#免费模型--free-models)
3. [付费模型 / Paid Models](#付费模型--paid-models)
4. [收费标准 / Pricing](#收费标准--pricing)
5. [免费API密钥限制 / Free API Key Limitations](#免费api密钥限制--free-api-key-limitations)
6. [使用建议 / Usage Recommendations](#使用建议--usage-recommendations)
7. [配置示例 / Configuration Examples](#配置示例--configuration-examples)

---

## 模型概览 / Models Overview

### 🆓 免费模型 / Free Models

| 模型名称 / Model Name | 描述 / Description | 上下文长度 / Context Length | 特点 / Features |
|----------------------|-------------------|---------------------------|-----------------|
| `gemini-1.5-flash` | 快速多模态模型 / Fast multimodal model | 1M tokens | ⚡ 快速响应<br/>🔍 支持搜索工具<br/>📷 多模态支持 |
| `gemini-1.5-flash-8b` | 轻量级快速模型 / Lightweight fast model | 1M tokens | ⚡ 超快响应<br/>💰 更低成本<br/>🔍 支持搜索工具 |

### 💰 付费模型 / Paid Models

| 模型名称 / Model Name | 描述 / Description | 上下文长度 / Context Length | 特点 / Features |
|----------------------|-------------------|---------------------------|-----------------|
| `gemini-1.5-pro` | 高性能多模态模型 / High-performance multimodal | 2M tokens | 🧠 最佳推理能力<br/>🔍 支持搜索工具<br/>📊 复杂分析 |
| `gemini-2.0-flash-exp` | 实验性2.0快速模型 / Experimental 2.0 fast | 1M tokens | 🚀 最新技术<br/>⚡ 快速响应<br/>🔬 实验特性 |
| `gemini-2.0-flash-thinking-exp` | 思维链2.0模型 / Chain-of-thought 2.0 | 32K tokens | 🤔 深度思考<br/>📝 推理过程<br/>🔬 实验特性 |
| `gemini-exp-1206` | 最新实验模型 / Latest experimental | 2M tokens | 🔬 前沿技术<br/>🧠 高级推理<br/>⚠️ 实验性质 |

---

## 免费模型 / Free Models

### 🚀 Gemini 1.5 Flash
**模型ID**: `gemini-1.5-flash`

**优势 / Advantages**:
- ✅ 完全免费使用 / Completely free to use
- ✅ 支持Google搜索工具 / Supports Google Search tools
- ✅ 快速响应时间 / Fast response time
- ✅ 多模态支持（文本、图像、音频、视频）/ Multimodal support
- ✅ 1M token上下文窗口 / 1M token context window

**适用场景 / Use Cases**:
- 日常问答 / Daily Q&A
- 网络搜索研究 / Web search research
- 多媒体内容分析 / Multimedia content analysis
- 原型开发 / Prototype development

### ⚡ Gemini 1.5 Flash-8B
**模型ID**: `gemini-1.5-flash-8b`

**优势 / Advantages**:
- ✅ 完全免费使用 / Completely free to use
- ✅ 超快响应速度 / Ultra-fast response
- ✅ 支持Google搜索工具 / Supports Google Search tools
- ✅ 轻量级设计 / Lightweight design
- ✅ 1M token上下文窗口 / 1M token context window

**适用场景 / Use Cases**:
- 快速查询 / Quick queries
- 实时应用 / Real-time applications
- 高频率调用 / High-frequency calls
- 资源受限环境 / Resource-constrained environments

---

## 付费模型 / Paid Models

### 🧠 Gemini 1.5 Pro
**模型ID**: `gemini-1.5-pro`

**优势 / Advantages**:
- 🎯 最佳推理和分析能力 / Best reasoning and analysis
- 📊 复杂任务处理 / Complex task handling
- 🔍 支持Google搜索工具 / Supports Google Search tools
- 📷 高级多模态能力 / Advanced multimodal capabilities
- 📚 2M token上下文窗口 / 2M token context window

**适用场景 / Use Cases**:
- 学术研究 / Academic research
- 复杂分析任务 / Complex analysis tasks
- 专业内容创作 / Professional content creation
- 高质量要求的应用 / High-quality applications

### 🚀 Gemini 2.0 Flash Experimental
**模型ID**: `gemini-2.0-flash-exp`

**优势 / Advantages**:
- 🔬 最新2.0技术 / Latest 2.0 technology
- ⚡ 快速响应 / Fast response
- 🆕 新特性和改进 / New features and improvements
- 🔍 支持搜索工具 / Supports search tools

**注意事项 / Notes**:
- ⚠️ 实验性质，可能不稳定 / Experimental, may be unstable
- 🔄 功能可能随时变化 / Features may change anytime

### 🤔 Gemini 2.0 Flash Thinking Experimental
**模型ID**: `gemini-2.0-flash-thinking-exp`

**优势 / Advantages**:
- 🧠 显示推理过程 / Shows reasoning process
- 🤔 深度思考能力 / Deep thinking capabilities
- 📝 步骤化解决问题 / Step-by-step problem solving
- 🔬 前沿思维链技术 / Cutting-edge chain-of-thought

**限制 / Limitations**:
- 📏 较短上下文窗口 (32K tokens) / Shorter context window
- ⚠️ 实验性质 / Experimental nature

### 🔬 Gemini Experimental 1206
**模型ID**: `gemini-exp-1206`

**优势 / Advantages**:
- 🆕 最新实验模型 / Latest experimental model
- 🧠 高级推理能力 / Advanced reasoning
- 📚 大上下文窗口 (2M tokens) / Large context window
- 🔬 前沿技术测试 / Cutting-edge technology testing

**注意事项 / Notes**:
- ⚠️ 高度实验性 / Highly experimental
- 🔄 可能随时下线 / May be discontinued anytime

---

## 收费标准 / Pricing

### 💰 Google AI Studio 定价 (2024年12月)

#### 免费层 / Free Tier
| 模型 / Model | 每分钟请求限制 / RPM | 每天请求限制 / RPD | 费用 / Cost |
|--------------|---------------------|-------------------|-------------|
| Gemini 1.5 Flash | 15 | 1,500 | **免费 / FREE** |
| Gemini 1.5 Flash-8B | 15 | 1,500 | **免费 / FREE** |

#### 付费层 / Paid Tier
| 模型 / Model | 输入价格 / Input Price | 输出价格 / Output Price | 上下文缓存 / Context Caching |
|--------------|----------------------|----------------------|---------------------------|
| **Gemini 1.5 Pro** | $1.25 / 1M tokens | $5.00 / 1M tokens | $0.3125 / 1M tokens |
| **Gemini 2.0 Flash Exp** | $0.075 / 1M tokens | $0.30 / 1M tokens | $0.01875 / 1M tokens |
| **Gemini 2.0 Flash Thinking** | $0.075 / 1M tokens | $0.30 / 1M tokens | - |
| **Gemini Exp-1206** | $0.075 / 1M tokens | $0.30 / 1M tokens | $0.01875 / 1M tokens |

### 💡 成本估算示例 / Cost Estimation Examples

#### 场景1: 日常使用 (免费模型)
```
模型: gemini-1.5-flash
使用量: 每天50次查询，平均每次1000 tokens
月成本: $0 (免费层内)
```

#### 场景2: 专业研究 (付费模型)
```
模型: gemini-1.5-pro
使用量: 每天100次查询，平均每次5000 tokens输入 + 2000 tokens输出
月成本: 约 $63 USD
计算: (5000 × 100 × 30 × $1.25/1M) + (2000 × 100 × 30 × $5.00/1M)
```

---

## 免费API密钥限制 / Free API Key Limitations

### 🚫 主要限制 / Main Limitations

#### 1. 速率限制 / Rate Limits
- **每分钟请求数 / Requests Per Minute**: 15
- **每天请求数 / Requests Per Day**: 1,500
- **并发请求 / Concurrent Requests**: 1

#### 2. 功能限制 / Feature Limitations
- ❌ 无法使用付费模型 / Cannot use paid models
- ❌ 无法进行商业用途 / No commercial use
- ❌ 有地区限制 / Geographic restrictions
- ❌ 无SLA保证 / No SLA guarantee

#### 3. 性能限制 / Performance Limitations
- ⏱️ 可能有延迟 / May experience latency
- 🔄 可能被限流 / May be throttled
- 📊 无优先级处理 / No priority processing

### 🔓 升级到付费的好处 / Benefits of Upgrading to Paid

#### 1. 更高限制 / Higher Limits
- **每分钟请求数**: 1,000+ / **RPM**: 1,000+
- **每天请求数**: 无限制 / **RPD**: Unlimited
- **并发请求**: 100+ / **Concurrent**: 100+

#### 2. 高级功能 / Advanced Features
- ✅ 访问所有模型 / Access to all models
- ✅ 商业使用许可 / Commercial use license
- ✅ SLA保证 / SLA guarantee
- ✅ 优先支持 / Priority support

#### 3. 更好性能 / Better Performance
- ⚡ 更快响应时间 / Faster response times
- 🔄 无限流 / No throttling
- 📊 优先级处理 / Priority processing

---

## 使用建议 / Usage Recommendations

### 🎯 模型选择策略 / Model Selection Strategy

#### 开发阶段 / Development Phase
```yaml
推荐配置 / Recommended:
  provider: "gemini"
  model: "gemini-1.5-flash"
  temperature: 0.6
  
原因 / Reason:
  - 免费使用 / Free to use
  - 快速迭代 / Fast iteration
  - 功能完整 / Full features
```

#### 生产环境 / Production Environment
```yaml
推荐配置 / Recommended:
  provider: "gemini"
  model: "gemini-1.5-pro"
  temperature: 0.4
  
原因 / Reason:
  - 最佳质量 / Best quality
  - 稳定性能 / Stable performance
  - 商业许可 / Commercial license
```

#### 成本敏感 / Cost-Sensitive
```yaml
推荐配置 / Recommended:
  provider: "auto"  # 优先DeepSeek
  fallback_model: "gemini-1.5-flash"
  temperature: 0.5
  
原因 / Reason:
  - 混合使用降低成本 / Hybrid usage reduces cost
  - 保持搜索功能 / Maintains search functionality
```

### 🔧 配置优化 / Configuration Optimization

#### 高频应用 / High-Frequency Applications
```python
# 使用最快的免费模型
GEMINI_MODEL = "gemini-1.5-flash-8b"
TEMPERATURE = 0.3  # 降低随机性
ENABLE_CACHING = True  # 启用缓存
```

#### 质量优先 / Quality-First
```python
# 使用最佳付费模型
GEMINI_MODEL = "gemini-1.5-pro"
TEMPERATURE = 0.4  # 平衡质量和创造性
MAX_TOKENS = 4096  # 更长输出
```

#### 实验性功能 / Experimental Features
```python
# 使用最新实验模型
GEMINI_MODEL = "gemini-exp-1206"
TEMPERATURE = 0.6  # 允许更多创造性
ENABLE_THINKING = True  # 启用思维过程
```

---

## 配置示例 / Configuration Examples

### 📝 环境变量配置 / Environment Variables

```bash
# .env 文件示例
# Basic Configuration
GEMINI_API_KEY=your_gemini_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Model Configuration
DEFAULT_GEMINI_MODEL=gemini-1.5-flash
FALLBACK_GEMINI_MODEL=gemini-1.5-flash-8b
PREMIUM_GEMINI_MODEL=gemini-1.5-pro

# Feature Flags
ENABLE_PAID_MODELS=false
ENABLE_EXPERIMENTAL_MODELS=false
USE_HYBRID_ARCHITECTURE=true

# Rate Limiting
MAX_REQUESTS_PER_MINUTE=10
MAX_REQUESTS_PER_DAY=1000
```

### 🛠️ 应用配置 / Application Configuration

```python
# config.py 示例
class GeminiConfig:
    # 免费层配置 / Free Tier Configuration
    FREE_MODELS = [
        "gemini-1.5-flash",
        "gemini-1.5-flash-8b"
    ]
    
    # 付费层配置 / Paid Tier Configuration  
    PAID_MODELS = [
        "gemini-1.5-pro",
        "gemini-2.0-flash-exp",
        "gemini-2.0-flash-thinking-exp",
        "gemini-exp-1206"
    ]
    
    # 默认配置 / Default Configuration
    DEFAULT_MODEL = "gemini-1.5-flash"
    DEFAULT_TEMPERATURE = 0.7
    DEFAULT_MAX_TOKENS = 2048
    
    # 速率限制 / Rate Limits
    FREE_TIER_RPM = 15
    FREE_TIER_RPD = 1500
    PAID_TIER_RPM = 1000
    PAID_TIER_RPD = -1  # Unlimited
```

---

## 🚨 重要提醒 / Important Notes

### ⚠️ 免费API密钥使用注意事项

1. **不要在生产环境使用免费密钥** / Don't use free keys in production
2. **遵守使用条款** / Follow terms of service  
3. **监控使用量** / Monitor usage
4. **准备升级计划** / Prepare upgrade plan

### 📈 升级时机 / When to Upgrade

- 超出免费限制时 / When exceeding free limits
- 需要商业使用时 / When commercial use needed
- 需要更好性能时 / When better performance needed
- 需要高级模型时 / When advanced models needed

### 🔒 安全建议 / Security Recommendations

- 🔐 保护API密钥安全 / Protect API keys
- 🚫 不要在客户端暴露密钥 / Don't expose keys in client
- 🔄 定期轮换密钥 / Rotate keys regularly
- 📊 监控异常使用 / Monitor unusual usage

---

---

## 🔗 相关文档 / Related Documentation

- 📖 [API密钥配置指南 / API Keys Setup Guide](./API_KEYS_SETUP.md)
- 🏗️ [系统架构分析 / Architecture Analysis](./ARCHITECTURE_ANALYSIS.md)
- 📚 [主要文档 / Main Documentation](../README.md)

---

**最后更新 / Last Updated**: 2024年12月 / December 2024
**文档版本 / Document Version**: 1.0
