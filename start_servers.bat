@echo off
echo Starting AstraFlow Servers...
echo.

echo Starting Chat Server (Port 3001)...
start "Chat Server" cmd /k "cd backend\src && python chat_server.py"

echo Waiting 3 seconds...
timeout /t 3 /nobreak >nul

echo Starting LangGraph Search Agent (Port 2024)...
start "Search Agent" cmd /k "cd backend && set PYTHONPATH=./src && langgraph dev"

echo.
echo Both servers are starting...
echo.
echo Chat Server: http://127.0.0.1:3001
echo Search Agent: http://127.0.0.1:2024
echo Search Studio: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024
echo.
echo Press any key to exit...
pause >nul
