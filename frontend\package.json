{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@langchain/core": "^0.3.55", "@langchain/langgraph-sdk": "^0.0.74", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@tailwindcss/vite": "^4.1.5", "@testing-library/react": "^16.3.0", "ag-grid-community": "^34.0.2", "ag-grid-react": "^34.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "lucide-react": "^0.508.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^9.0.3", "react-router-dom": "^7.5.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/node": "^22.15.17", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "@types/react-syntax-highlighter": "^15.5.13", "@types/xlsx": "^0.0.35", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tw-animate-css": "^1.2.9", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.4"}}